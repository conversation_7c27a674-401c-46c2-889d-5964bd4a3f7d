import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { UserProviderShipmentService } from '../services/user-provider-shipment.service';
import { ProviderShipmentType } from '../constants/provider-shipment-type.enum';
import {
  CreateUserProviderShipmentDto,
  UpdateUserProviderShipmentDto,
  UserProviderShipmentResponseDto
} from '../dto/user-provider-shipment';
import { PaginatedResult } from '@common/response';

/**
 * Controller quản lý cấu hình provider shipment của user
 */
@ApiTags('User Provider Shipment')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('user/provider-shipments')
export class UserProviderShipmentController {
  private readonly logger = new Logger(UserProviderShipmentController.name);

  constructor(
    private readonly userProviderShipmentService: UserProviderShipmentService
  ) {}

  /**
   * Tạo cấu hình provider shipment mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo cấu hình provider shipment mới',
    description: `Tạo cấu hình mới cho GHTK hoặc GHN với thông tin API credentials.

    **Lưu ý quan trọng:**
    - Mỗi user chỉ được có tối đa 1 cấu hình cho mỗi loại provider (GHN hoặc GHTK)
    - Hệ thống sẽ test connection trước khi lưu cấu hình
    - Token sẽ được mã hóa và trả về dạng ***ENCRYPTED*** để bảo mật
    - Nếu token không hợp lệ, hệ thống sẽ báo lỗi ngay lập tức`
  })
  @ApiBody({
    description: 'Thông tin cấu hình provider shipment',
    examples: {
      'GHTK Configuration': {
        summary: 'Cấu hình GHTK',
        description: 'Ví dụ cấu hình cho GHTK với token API',
        value: {
          name: 'Cấu hình GHTK chính',
          type: 'GHTK',
          ghtkConfig: {
            token: '8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou',
            timeout: 30000,
            isTestMode: true
          }
        }
      },
      'GHN Configuration': {
        summary: 'Cấu hình GHN',
        description: 'Ví dụ cấu hình cho GHN với token và shop ID',
        value: {
          name: 'Cấu hình GHN chính',
          type: 'GHN',
          ghnConfig: {
            token: '42d0fc57-402d-11f0-9b81-222185cb68c8',
            shopId: '196768',
            timeout: 30000,
            isTestMode: true
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo cấu hình thành công',
    examples: {
      'GHTK Response': {
        summary: 'Response cho GHTK',
        value: {
          id: 'uuid-string-ghtk',
          name: 'Cấu hình GHTK chính',
          type: 'GHTK',
          ghtkConfig: {
            baseUrl: 'https://services-staging.ghtklab.com',
            timeout: 30000,
            isTestMode: true,
            token: '8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou',
            hasToken: true
          },
          createdAt: 1640995200000
        }
      },
      'GHN Response': {
        summary: 'Response cho GHN',
        value: {
          id: 'uuid-string-ghn',
          name: 'Cấu hình GHN chính',
          type: 'GHN',
          ghnConfig: {
            baseUrl: 'https://dev-online-gateway.ghn.vn',
            timeout: 30000,
            isTestMode: true,
            token: '42d0fc57-402d-11f0-9b81-222185cb68c8',
            shopId: '196768',
            hasToken: true,
            hasShopId: true
          },
          createdAt: 1640995200000
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ hoặc đã có cấu hình cho provider này hoặc test connection thất bại'
  })
  @ApiResponse({
    status: 409,
    description: 'User đã có cấu hình cho provider này (chỉ được phép 1 cấu hình mỗi loại)'
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateUserProviderShipmentDto
  ): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`User ${user.id} tạo cấu hình ${dto.type}`);
      return await this.userProviderShipmentService.create(user.id, dto);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Lấy danh sách cấu hình provider shipment
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách cấu hình provider shipment',
    description: 'Lấy danh sách tất cả cấu hình provider shipment của user với phân trang'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Số trang (mặc định: 1)',
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng item mỗi trang (mặc định: 10)',
    example: 10
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ProviderShipmentType,
    description: 'Lọc theo loại provider'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: 'PaginatedResult<UserProviderShipmentResponseDto>'
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('type') type?: ProviderShipmentType
  ): Promise<PaginatedResult<UserProviderShipmentResponseDto>> {
    try {
      this.logger.log(`User ${user.id} lấy danh sách cấu hình`);
      return await this.userProviderShipmentService.findByUserId(user.id, page, limit, type);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Lấy cấu hình provider shipment theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy cấu hình provider shipment theo ID',
    description: 'Lấy chi tiết cấu hình provider shipment theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của cấu hình',
    example: 'uuid-string'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy cấu hình thành công',
    type: UserProviderShipmentResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy cấu hình'
  })
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string
  ): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`User ${user.id} lấy cấu hình ${id}`);
      return await this.userProviderShipmentService.findById(user.id, id);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật cấu hình provider shipment
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật cấu hình provider shipment',
    description: 'Cập nhật thông tin cấu hình provider shipment'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của cấu hình',
    example: 'uuid-string'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật cấu hình thành công',
    type: UserProviderShipmentResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy cấu hình'
  })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() dto: UpdateUserProviderShipmentDto
  ): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`User ${user.id} cập nhật cấu hình ${id}`);
      return await this.userProviderShipmentService.update(user.id, id, dto);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Xóa cấu hình provider shipment
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa cấu hình provider shipment',
    description: 'Xóa cấu hình provider shipment theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của cấu hình',
    example: 'uuid-string'
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa cấu hình thành công'
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy cấu hình'
  })
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string
  ): Promise<{ message: string }> {
    try {
      this.logger.log(`User ${user.id} xóa cấu hình ${id}`);
      await this.userProviderShipmentService.delete(user.id, id);
      return { message: 'Xóa cấu hình thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa cấu hình:`, error);
      throw error;
    }
  }






}
