/**
 * Interface định nghĩa cấu trúc của cấu hình <PERSON>ng dụng
 */
export interface AppConfig {
  // Server
  port: number;
  nodeEnv: string;
  apiPrefix: string;

  // Database
  database: DatabaseConfig;

  // Storage
  storage: StorageConfig;

  // Authentication
  auth: AuthConfig;

  // S3
  s3: S3Config;

  // Secrect Key Model
  secrectKeyModel: SecretKeyModelConfig;

  // Facebook
  facebook: FacebookConfig;

  // Email
  email: EmailConfig;

  // Redis
  redis: RedisConfig;

  // Shipment
  shipment: ShipmentConfig;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình secret key model
 */
export interface SecretKeyModelConfig {
  adminSecretKey: string;
  userSecretKey: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình database
 */
export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình storage
 */
export interface StorageConfig {
  cloudflare: {
    region: string;
    accessKey: string;
    secretKey: string;
    endpoint: string;
    bucketName: string;
  };
  cdn: {
    url: string;
    secretKey: string;
  };
}

/**
 * Interface định nghĩa cấu trúc của cấu hình authentication
 */
export interface AuthConfig {
  jwt: {
    secret: string;
    expirationTime: string;
    refreshSecret?: string;
    refreshExpirationTime?: string;
  };
}

// Redis
export interface RedisConfig {
  url: string;
  password?: string;
}

// Email
export interface EmailConfig {
  apiUrl?: string;
  smtpHost?: string;
  smtpPort?: number;
  smtpUser?: string;
  smtpPass?: string;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình Facebook
 */
export interface FacebookConfig {
  appId: string;
  appSecret: string;
  graphApiVersion: string;
  redirectUri: string;
}

export interface S3Config {
  s3?:{
    endpoint: string;
    accessKey: string;
    secretAccessKey: string;
    region: string;
    bucketName: string;
  },
  cdn?:{
    url: string;
    secretKey: string;
  }
}

export  interface  ShipmentConfig {
  ghtk: string;
  ghn: string;
}
