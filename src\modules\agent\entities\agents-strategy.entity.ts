import { Column, Entity, PrimaryColumn } from 'typeorm';
import { IStrategyContentStep } from '../interfaces/strategy-content-step.interface';

/**
 * Entity đại diện cho bảng agents_strategy trong cơ sở dữ liệu
 * Lưu trữ chiến lược xử lý của agent, liên kết với model cụ thể và khóa API tương ứng
 */
@Entity('agents_strategy')
export class AgentStrategy {
  /**
   * Khóa chính đồng thời là ID của agent, liên kết 1-1 với bảng agents
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * Nội dung chiến lược của agent (cấu hình, rule...) dưới dạng JSON
   */
  @Column({ name: 'content', type: 'jsonb', default: '[]' })
  content: IStrategyContentStep[];

  /**
   * <PERSON><PERSON><PERSON> ví dụ mẫu để minh họa hoặc hướng dẫn chiến lược, định dạng JSON
   */
  @Column({ name: 'example_default', type: 'jsonb', default: '[]' })
  exampleDefault: IStrategyContentStep[];

  /**
   * ID nhân viên tạo chiến lược, tham chiếu bảng employees
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID nhân viên chỉnh sửa gần nhất, tham chiếu bảng employees
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * ID nhân viên đã xóa chiến lược, tham chiếu bảng employees
   */
  @Column({ name: 'deleted_by', type: 'integer', nullable: true })
  deletedBy: number | null;

  /**
   * Tên của mô hình AI được sử dụng trong chiến lược (ví dụ: gpt-4)
   */
  @Column({ name: 'model_name', type: 'varchar', length: 255, nullable: false })
  modelName: string;

  /**
   * Tham chiếu đến bảng model_registry, mô tả khả năng của mô hình
   */
  @Column({ name: 'model_registry_id', type: 'uuid', nullable: false })
  modelRegistryId: string;

  /**
   * Tham chiếu đến bảng system_key_llm, chứa khóa API dùng để gọi mô hình AI
   */
  @Column({ name: 'key_llm_id', type: 'uuid', nullable: false })
  keyLlmId: string;
}
